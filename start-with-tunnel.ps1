# PowerShell script untuk start ATMA Backend dengan Cloudflare Tunnel
# Usage: .\start-with-tunnel.ps1

Write-Host "🚀 Starting ATMA Backend with Cloudflare Tunnel..." -ForegroundColor Green

# Check if .env.cloudflare exists
if (-not (Test-Path ".env.cloudflare")) {
    Write-Host "❌ File .env.cloudflare tidak ditemukan!" -ForegroundColor Red
    Write-Host "📝 Silakan buat file .env.cloudflare dan isi CLOUDFLARE_TUNNEL_TOKEN" -ForegroundColor Yellow
    Write-Host "📖 Lihat setup-cloudflare-tunnel.md untuk panduan lengkap" -ForegroundColor Yellow
    exit 1
}

# Load environment variables from .env.cloudflare
Write-Host "📄 Loading Cloudflare environment variables..." -ForegroundColor Blue
Get-Content ".env.cloudflare" | ForEach-Object {
    if ($_ -match "^([^#][^=]+)=(.*)$") {
        $name = $matches[1].Trim()
        $value = $matches[2].Trim()
        [Environment]::SetEnvironmentVariable($name, $value, "Process")
        Write-Host "   ✓ $name" -ForegroundColor Gray
    }
}

# Check if tunnel token is set
$tunnelToken = [Environment]::GetEnvironmentVariable("CLOUDFLARE_TUNNEL_TOKEN", "Process")
if ([string]::IsNullOrEmpty($tunnelToken) -or $tunnelToken -eq "your_tunnel_token_here") {
    Write-Host "❌ CLOUDFLARE_TUNNEL_TOKEN belum diset dengan benar!" -ForegroundColor Red
    Write-Host "📝 Silakan update file .env.cloudflare dengan tunnel token yang valid" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Tunnel token loaded successfully" -ForegroundColor Green

# Start Docker Compose
Write-Host "🐳 Starting Docker services..." -ForegroundColor Blue
docker-compose up -d

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ All services started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Your services will be available at:" -ForegroundColor Cyan
    Write-Host "   • API Gateway: https://api.chhrone.web.id" -ForegroundColor White
    Write-Host "   • Documentation: https://docs.chhrone.web.id" -ForegroundColor White
    Write-Host ""
    Write-Host "📊 To check tunnel status:" -ForegroundColor Yellow
    Write-Host "   docker-compose logs cloudflare-tunnel" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🔍 To check all services:" -ForegroundColor Yellow
    Write-Host "   docker-compose ps" -ForegroundColor Gray
} else {
    Write-Host "❌ Failed to start services!" -ForegroundColor Red
    Write-Host "🔍 Check logs with: docker-compose logs" -ForegroundColor Yellow
}
