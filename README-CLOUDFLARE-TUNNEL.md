# ATMA Backend - Cloudflare Zero Trust Tunnel Setup

Panduan lengkap untuk mengekspos ATMA Backend menggunakan Cloudflare Zero Trust Tunnel.

## 🎯 Tujuan
- **API Gateway** (localhost:3000) → **api.chhrone.web.id**
- **Documentation Server** (localhost:3006) → **docs.chhrone.web.id**

## 📋 Prerequisites
1. ✅ Domain `chhrone.web.id` terdaftar di Cloudflare
2. ✅ Nameserver domain sudah diubah ke Cloudflare
3. ✅ Akun Cloudflare Zero Trust (gratis)
4. ✅ Docker & Docker Compose terinstall

## 🚀 Quick Start

### 1. Setup Cloudflare Tunnel
```bash
# Baca panduan lengkap
cat setup-cloudflare-tunnel.md
```

### 2. Konfigurasi Token
```bash
# Copy template environment
cp .env.cloudflare.example .env.cloudflare

# Edit dan masukkan tunnel token
nano .env.cloudflare
```

### 3. Start Services
**Windows (PowerShell):**
```powershell
.\start-with-tunnel.ps1
```

**Linux/Mac:**
```bash
chmod +x start-with-tunnel.sh
./start-with-tunnel.sh
```

**Manual:**
```bash
# Load environment variables
source .env.cloudflare

# Start all services
docker-compose up -d
```

## 🔧 Files Added/Modified

### New Files:
- `cloudflare-tunnel-config.yml` - Tunnel configuration (alternative method)
- `.env.cloudflare` - Environment variables for tunnel token
- `setup-cloudflare-tunnel.md` - Detailed setup guide
- `start-with-tunnel.ps1` - Windows PowerShell startup script
- `start-with-tunnel.sh` - Linux/Mac bash startup script
- `README-CLOUDFLARE-TUNNEL.md` - This file

### Modified Files:
- `docker-compose.yml` - Added cloudflare-tunnel service
- `.gitignore` - Added cloudflare-related files

## 🌐 Service Architecture

```
Internet
    ↓
Cloudflare Edge
    ↓
Cloudflare Tunnel (Docker)
    ↓
┌─────────────────────────────────┐
│ Docker Network (atma-network)   │
│                                 │
│ api.chhrone.web.id             │
│     ↓                          │
│ api-gateway:3000               │
│                                 │
│ docs.chhrone.web.id            │
│     ↓                          │
│ documentation-service:80       │
└─────────────────────────────────┘
```

## 🔍 Monitoring & Troubleshooting

### Check Tunnel Status
```bash
docker-compose logs cloudflare-tunnel
```

### Check All Services
```bash
docker-compose ps
```

### Test Endpoints
```bash
# Local endpoints (should work)
curl http://localhost:3000/health
curl http://localhost:3006

# Public endpoints (after tunnel setup)
curl https://api.chhrone.web.id/health
curl https://docs.chhrone.web.id
```

### Common Issues

**1. Tunnel Token Invalid**
```
Error: failed to authenticate connection
```
- Pastikan token di `.env.cloudflare` benar
- Generate token baru dari Cloudflare Dashboard

**2. DNS Not Resolving**
```
nslookup api.chhrone.web.id
```
- Tunggu DNS propagation (up to 24 hours)
- Pastikan domain menggunakan Cloudflare nameserver

**3. Service Not Reachable**
```bash
# Check if services are running
docker-compose ps

# Check service logs
docker-compose logs api-gateway
docker-compose logs documentation-service
```

## 🔒 Security Considerations

1. **Environment Variables**: Never commit `.env.cloudflare` to git
2. **Tunnel Token**: Treat as sensitive credential
3. **Access Control**: Consider using Cloudflare Access policies
4. **HTTPS Only**: All traffic automatically encrypted via Cloudflare

## 📊 Performance Notes

- Cloudflare Tunnel adds minimal latency (~10-50ms)
- Automatic DDoS protection via Cloudflare
- Global CDN caching for static assets
- Automatic SSL/TLS termination

## 🔄 Updates & Maintenance

### Update Tunnel
```bash
# Pull latest cloudflared image
docker-compose pull cloudflare-tunnel

# Restart tunnel service
docker-compose restart cloudflare-tunnel
```

### Backup Configuration
```bash
# Backup tunnel configuration
cp .env.cloudflare .env.cloudflare.backup
```

## 📞 Support

Jika mengalami masalah:
1. Cek logs dengan `docker-compose logs cloudflare-tunnel`
2. Verifikasi konfigurasi di Cloudflare Dashboard
3. Test koneksi lokal terlebih dahulu
4. Cek DNS propagation dengan online tools

## 🎉 Success Indicators

Setelah setup berhasil, Anda akan melihat:
- ✅ Tunnel status "Connected" di Cloudflare Dashboard
- ✅ `https://api.chhrone.web.id/health` returns 200 OK
- ✅ `https://docs.chhrone.web.id` shows documentation
- ✅ No errors in `docker-compose logs cloudflare-tunnel`
