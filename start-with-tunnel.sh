#!/bin/bash
# Bash script untuk start ATMA Backend dengan Cloudflare Tunnel
# Usage: ./start-with-tunnel.sh

echo "🚀 Starting ATMA Backend with Cloudflare Tunnel..."

# Check if .env.cloudflare exists
if [ ! -f ".env.cloudflare" ]; then
    echo "❌ File .env.cloudflare tidak ditemukan!"
    echo "📝 Silakan buat file .env.cloudflare dan isi CLOUDFLARE_TUNNEL_TOKEN"
    echo "📖 Lihat setup-cloudflare-tunnel.md untuk panduan lengkap"
    exit 1
fi

# Load environment variables from .env.cloudflare
echo "📄 Loading Cloudflare environment variables..."
export $(grep -v '^#' .env.cloudflare | xargs)

# Check if tunnel token is set
if [ -z "$CLOUDFLARE_TUNNEL_TOKEN" ] || [ "$CLOUDFLARE_TUNNEL_TOKEN" = "your_tunnel_token_here" ]; then
    echo "❌ CLOUDFLARE_TUNNEL_TOKEN belum diset dengan benar!"
    echo "📝 Silakan update file .env.cloudflare dengan tunnel token yang valid"
    exit 1
fi

echo "✅ Tunnel token loaded successfully"

# Start Docker Compose
echo "🐳 Starting Docker services..."
docker-compose up -d

if [ $? -eq 0 ]; then
    echo "✅ All services started successfully!"
    echo ""
    echo "🌐 Your services will be available at:"
    echo "   • API Gateway: https://api.chhrone.web.id"
    echo "   • Documentation: https://docs.chhrone.web.id"
    echo ""
    echo "📊 To check tunnel status:"
    echo "   docker-compose logs cloudflare-tunnel"
    echo ""
    echo "🔍 To check all services:"
    echo "   docker-compose ps"
else
    echo "❌ Failed to start services!"
    echo "🔍 Check logs with: docker-compose logs"
fi
