# Setup Cloudflare Zero Trust Tunnel

## Prerequisites
1. Domain `chhrone.web.id` sudah terdaftar di Cloudflare
2. Nameserver domain sudah diubah ke Cloudflare nameserver
3. Akun Cloudflare Zero Trust (gratis)

## Step-by-Step Setup

### 1. Login ke Cloudflare Zero Trust Dashboard
- Buka https://one.dash.cloudflare.com/
- Login dengan akun Cloudflare Anda

### 2. Buat Tunnel Baru
1. Navigate ke **Access** > **Tunnels**
2. Klik **Create a tunnel**
3. Pilih **Cloudflared**
4. Beri nama tunnel, misalnya: `atma-backend-tunnel`
5. Klik **Save tunnel**

### 3. Install Connector (Skip - Kita pakai Docker)
- Skip langkah install connector karena kita akan menggunakan Docker

### 4. Route Traffic
Tambahkan routing berikut:

**Public Hostname 1:**
- Subdomain: `api`
- Domain: `chhrone.web.id`
- Path: (kosongkan)
- Service Type: `HTTP`
- URL: `api-gateway:3000`

**Public Hostname 2:**
- Subdomain: `docs`
- Domain: `chhrone.web.id`
- Path: (kosongkan)
- Service Type: `HTTP`
- URL: `documentation-service:80`

### 5. Copy Tunnel Token
1. Setelah selesai setup routing, klik pada tunnel yang baru dibuat
2. Klik tab **Configure**
3. Copy **Tunnel Token** (dimulai dengan `eyJ...`)
4. Paste token tersebut ke file `.env.cloudflare`:
   ```
   CLOUDFLARE_TUNNEL_TOKEN=eyJhIjoiYWJjZGVmZ2hpams...
   ```

### 6. Update Docker Compose
File `docker-compose.yml` sudah diupdate dengan service cloudflare-tunnel.

### 7. Start Services
```bash
# Load environment variables
source .env.cloudflare

# Start all services including Cloudflare tunnel
docker-compose up -d

# Check tunnel status
docker-compose logs cloudflare-tunnel
```

### 8. Verify Setup
1. Tunggu beberapa menit untuk DNS propagation
2. Test endpoints:
   - https://api.chhrone.web.id/health
   - https://docs.chhrone.web.id

## Troubleshooting

### Check Tunnel Status
```bash
docker-compose logs cloudflare-tunnel
```

### Check Service Health
```bash
docker-compose ps
```

### DNS Issues
- Pastikan domain sudah menggunakan Cloudflare nameserver
- Tunggu DNS propagation (bisa sampai 24 jam)
- Cek DNS dengan: `nslookup api.chhrone.web.id`

### Tunnel Connection Issues
- Pastikan tunnel token benar
- Cek firewall/network restrictions
- Restart tunnel service: `docker-compose restart cloudflare-tunnel`

## Security Notes
1. Tunnel token adalah credential sensitif - jangan commit ke git
2. Tambahkan `.env.cloudflare` ke `.gitignore`
3. Gunakan Cloudflare Access policies untuk additional security jika diperlukan

## Alternative Configuration (Manual)
Jika ingin menggunakan konfigurasi file instead of token:

1. Download credentials file dari Cloudflare Dashboard
2. Update docker-compose.yml untuk mount credentials file
3. Gunakan `cloudflare-tunnel-config.yml` untuk konfigurasi routing
