# Cloudflare Zero Trust Tunnel Setup untuk ATMA Backend

Setup ini akan mengekspos layanan ATMA Backend melalui Cloudflare Tunnel:
- **API Gateway** (localhost:3000) → https://api.chhrone.web.id
- **Documentation Service** (localhost:3006) → https://docs.chhrone.web.id

## Prerequisites

1. Domain `chhrone.web.id` sudah terdaftar di Cloudflare
2. Nameserver domain sudah diubah ke Cloudflare
3. Docker dan Docker Compose sudah terinstall
4. PowerShell dengan akses Administrator

## Langkah-langkah Setup

### 1. Setup Awal Cloudflare Tunnel

Jalankan script setup sebagai Administrator:

```powershell
# Buka PowerShell sebagai Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\setup-cloudflare-tunnel.ps1
```

Script ini akan:
- Download `cloudflared.exe`
- Membuka browser untuk login ke Cloudflare
- Membuat tunnel baru bernama `atma-backend-tunnel`
- Membuat DNS records untuk `api.chhrone.web.id` dan `docs.chhrone.web.id`

### 2. Verifikasi DNS Records

Setelah setup, cek di Cloudflare Dashboard → DNS Records:
- `api` CNAME record pointing to tunnel
- `docs` CNAME record pointing to tunnel

### 3. Jalankan Docker Services

Pastikan semua layanan Docker berjalan:

```powershell
docker-compose up -d
```

Tunggu sampai semua service healthy (bisa dicek dengan `docker-compose ps`).

### 4. Jalankan Tunnel

#### Opsi A: Manual (untuk testing)
```powershell
.\run-tunnel.ps1
```

#### Opsi B: Install sebagai Windows Service (recommended untuk production)
```powershell
# Jalankan sebagai Administrator
.\install-tunnel-service.ps1
```

## Konfigurasi Files

### cloudflare-tunnel-config.yml
File konfigurasi tunnel yang mendefinisikan routing:
- `api.chhrone.web.id` → `http://localhost:3000`
- `docs.chhrone.web.id` → `http://localhost:3006`

## Testing

Setelah tunnel berjalan, test akses:

```bash
# Test API Gateway
curl https://api.chhrone.web.id/health

# Test Documentation
curl https://docs.chhrone.web.id
```

Atau buka di browser:
- https://api.chhrone.web.id
- https://docs.chhrone.web.id

## Troubleshooting

### 1. Tunnel tidak bisa connect
- Pastikan login ke Cloudflare berhasil
- Cek apakah tunnel sudah dibuat: `.\cloudflared.exe tunnel list`
- Cek DNS records di Cloudflare Dashboard

### 2. Service tidak bisa diakses
- Pastikan Docker services berjalan: `docker-compose ps`
- Cek port 3000 dan 3006 tidak diblok firewall
- Cek logs: `docker-compose logs api-gateway` dan `docker-compose logs documentation-service`

### 3. SSL/TLS Issues
- Cloudflare secara otomatis menyediakan SSL certificate
- Pastikan SSL/TLS mode di Cloudflare Dashboard set ke "Full" atau "Full (strict)"

## Management Commands

### Manual Tunnel Management
```powershell
# Start tunnel
.\cloudflared.exe tunnel --config cloudflare-tunnel-config.yml run atma-backend-tunnel

# List tunnels
.\cloudflared.exe tunnel list

# Delete tunnel (jika perlu)
.\cloudflared.exe tunnel delete atma-backend-tunnel
```

### Service Management (jika diinstall sebagai service)
```powershell
# Start service
Start-Service -Name CloudflareATMATunnel

# Stop service
Stop-Service -Name CloudflareATMATunnel

# Check status
Get-Service -Name CloudflareATMATunnel

# Uninstall service
.\cloudflared.exe service uninstall
```

## Security Notes

1. **Internal Service Key**: Pastikan `INTERNAL_SERVICE_KEY` di docker-compose.yml aman
2. **JWT Secret**: Pastikan `JWT_SECRET` menggunakan key yang kuat
3. **CORS**: Review setting `ALLOWED_ORIGINS` di API Gateway
4. **Rate Limiting**: Sudah dikonfigurasi di API Gateway (5000 requests per 15 menit)

## Monitoring

- Cloudflare Dashboard → Zero Trust → Access → Tunnels
- Docker logs: `docker-compose logs -f`
- Tunnel logs akan muncul di console atau Windows Event Log (jika service)

## Next Steps

Setelah tunnel berjalan:
1. Update frontend aplikasi untuk menggunakan `https://api.chhrone.web.id`
2. Setup monitoring dan alerting
3. Configure Cloudflare security features (WAF, DDoS protection, etc.)
4. Setup backup dan disaster recovery
