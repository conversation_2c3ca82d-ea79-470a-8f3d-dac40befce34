# ATMA Backend - Cloudflare Zero Trust Tunnel Setup

Panduan lengkap untuk mengekspos ATMA Backend menggunakan Cloudflare Zero Trust Tunnel.

## 🎯 Tujuan
- **API Gateway** (localhost:3000) → **api.chhrone.web.id**
- **Documentation Server** (localhost:3006) → **docs.chhrone.web.id**

## 📋 Prerequisites
1. ✅ Domain `chhrone.web.id` terdaftar di Cloudflare
2. ✅ Nameserver domain sudah diubah ke Cloudflare
3. ✅ Akun Cloudflare Zero Trust (gratis)
4. ✅ Docker & Docker Compose terinstall

## 🚀 Step-by-Step Setup

### 1. Login ke Cloudflare Zero Trust Dashboard
- Buka https://one.dash.cloudflare.com/
- Login dengan akun Cloudflare Anda

### 2. Buat Tunnel Baru
1. Navigate ke **Access** > **Tunnels**
2. Klik **Create a tunnel**
3. Pilih **Cloudflared**
4. <PERSON>ri nama tunnel, misalnya: `atma-backend-tunnel`
5. Klik **Save tunnel**

### 3. Install Connector (Skip - Kita pakai Docker)
- <PERSON><PERSON> langkah install connector karena kita akan menggunakan Docker

### 4. Route Traffic
Tambahkan routing berikut:

**Public Hostname 1:**
- Subdomain: `api`
- Domain: `chhrone.web.id`
- Path: (kosongkan)
- Service Type: `HTTP`
- URL: `api-gateway:3000`

**Public Hostname 2:**
- Subdomain: `docs`
- Domain: `chhrone.web.id`
- Path: (kosongkan)
- Service Type: `HTTP`
- URL: `documentation-service:80`

### 5. Copy Tunnel Token
1. Setelah selesai setup routing, klik pada tunnel yang baru dibuat
2. Klik tab **Configure**
3. Copy **Tunnel Token** (dimulai dengan `eyJ...`)
4. Paste token tersebut ke file `.env.cloudflare`:
   ```
   CLOUDFLARE_TUNNEL_TOKEN=eyJhIjoiYWJjZGVmZ2hpams...
   ```

### 6. Konfigurasi Token Lokal
```bash
# Copy template environment
cp .env.cloudflare.example .env.cloudflare

# Edit dan masukkan tunnel token
nano .env.cloudflare
```

### 7. Start Services
**Windows (PowerShell):**
```powershell
.\start-with-tunnel.ps1
```

**Linux/Mac:**
```bash
chmod +x start-with-tunnel.sh
./start-with-tunnel.sh
```

**Manual:**
```bash
# Load environment variables
source .env.cloudflare

# Start all services
docker-compose up -d
```

### 8. Verify Setup
1. Tunggu beberapa menit untuk DNS propagation
2. Test endpoints:
   - https://api.chhrone.web.id/health
   - https://docs.chhrone.web.id

## 🔧 Files Added/Modified

### New Files:
- `cloudflare-tunnel-config.yml` - Tunnel configuration (alternative method)
- `.env.cloudflare` - Environment variables for tunnel token
- `setup-cloudflare-tunnel.md` - Detailed setup guide
- `start-with-tunnel.ps1` - Windows PowerShell startup script
- `start-with-tunnel.sh` - Linux/Mac bash startup script
- `README-CLOUDFLARE-TUNNEL.md` - This file

### Modified Files:
- `docker-compose.yml` - Added cloudflare-tunnel service
- `.gitignore` - Added cloudflare-related files

## 🌐 Service Architecture

```
Internet
    ↓
Cloudflare Edge
    ↓
Cloudflare Tunnel (Docker)
    ↓
┌─────────────────────────────────┐
│ Docker Network (atma-network)   │
│                                 │
│ api.chhrone.web.id             │
│     ↓                          │
│ api-gateway:3000               │
│                                 │
│ docs.chhrone.web.id            │
│     ↓                          │
│ documentation-service:80       │
└─────────────────────────────────┘
```

## 🔍 Monitoring & Troubleshooting

### Check Tunnel Status
```bash
docker-compose logs cloudflare-tunnel
```

### Check All Services
```bash
docker-compose ps
```

### Test Endpoints
```bash
# Local endpoints (should work)
curl http://localhost:3000/health
curl http://localhost:3006

# Public endpoints (after tunnel setup)
curl https://api.chhrone.web.id/health
curl https://docs.chhrone.web.id
```

### Common Issues

**1. Tunnel Token Invalid**
```
Error: failed to authenticate connection
```
- Pastikan token di `.env.cloudflare` benar
- Generate token baru dari Cloudflare Dashboard

**2. DNS Not Resolving**
```
nslookup api.chhrone.web.id
```
- Tunggu DNS propagation (up to 24 hours)
- Pastikan domain menggunakan Cloudflare nameserver

**3. Service Not Reachable**
```bash
# Check if services are running
docker-compose ps

# Check service logs
docker-compose logs api-gateway
docker-compose logs documentation-service
```

## 🔒 Security Considerations

1. **Environment Variables**: Never commit `.env.cloudflare` to git
2. **Tunnel Token**: Treat as sensitive credential
3. **Access Control**: Consider using Cloudflare Access policies
4. **HTTPS Only**: All traffic automatically encrypted via Cloudflare

## 📊 Performance Notes

- Cloudflare Tunnel adds minimal latency (~10-50ms)
- Automatic DDoS protection via Cloudflare
- Global CDN caching for static assets
- Automatic SSL/TLS termination

## 🔄 Updates & Maintenance

### Update Tunnel
```bash
# Pull latest cloudflared image
docker-compose pull cloudflare-tunnel

# Restart tunnel service
docker-compose restart cloudflare-tunnel
```

### Backup Configuration
```bash
# Backup tunnel configuration
cp .env.cloudflare .env.cloudflare.backup
```

## 📞 Support

Jika mengalami masalah:
1. Cek logs dengan `docker-compose logs cloudflare-tunnel`
2. Verifikasi konfigurasi di Cloudflare Dashboard
3. Test koneksi lokal terlebih dahulu
4. Cek DNS propagation dengan online tools

## 🎉 Success Indicators

Setelah setup berhasil, Anda akan melihat:
- ✅ Tunnel status "Connected" di Cloudflare Dashboard
- ✅ `https://api.chhrone.web.id/health` returns 200 OK
- ✅ `https://docs.chhrone.web.id` shows documentation
- ✅ No errors in `docker-compose logs cloudflare-tunnel`
