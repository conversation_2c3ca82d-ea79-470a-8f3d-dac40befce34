# Cloudflare Tunnel Configuration
# Replace with your actual tunnel token from Cloudflare Zero Trust Dashboard
CLOUDFLARE_TUNNEL_TOKEN=eyJhIjoiYjVjNmQ1YTgwMDJhNjZkM2M4YmQ0YWM0MzkwOWE0NjQiLCJ0IjoiNmE1ZDkxYTktNDg3Ni00MzVjLTliMTEtMDE3OTdiZTAyYjQ4IiwicyI6IlkyUXdOV1JsTlRZdE9XWTFOQzAwTkRVMUxXRmlPRFV0TkRWbU4ySTBOelptTkdRMyJ9

# Instructions:
# 1. Go to Cloudflare Zero Trust Dashboard (https://one.dash.cloudflare.com/)
# 2. Navigate to Access > Tunnels
# 3. Create a new tunnel named 'atma-backend-tunnel'
# 4. Add Public Hostnames:
#    - Subdomain: api, Domain: chhrone.web.id, Service: HTTP, URL: api-gateway:3000
#    - Subdomain: docs, Domain: chhrone.web.id, Service: HTTP, URL: documentation-service:80
# 5. Copy the tunnel token and replace 'your_tunnel_token_here' above
# 6. Save this file and run: ./start-with-tunnel.ps1 (Windows) or ./start-with-tunnel.sh (Linux/Mac)
