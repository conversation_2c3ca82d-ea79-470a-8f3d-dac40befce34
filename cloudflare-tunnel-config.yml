tunnel: YOUR_TUNNEL_ID
credentials-file: /etc/cloudflared/cert.pem

ingress:
  # API Gateway - api.chhrone.web.id
  - hostname: api.chhrone.web.id
    service: http://api-gateway:3000
    originRequest:
      httpHostHeader: api.chhrone.web.id
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 10
      keepAliveTimeout: 90s

  # Documentation Server - docs.chhrone.web.id  
  - hostname: docs.chhrone.web.id
    service: http://documentation-service:80
    originRequest:
      httpHostHeader: docs.chhrone.web.id
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 10
      keepAliveTimeout: 90s

  # Catch-all rule (required)
  - service: http_status:404
